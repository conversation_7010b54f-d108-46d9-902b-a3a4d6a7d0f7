{"name": "del", "version": "4.1.1", "description": "Delete files and folders", "license": "MIT", "repository": "sindresorhus/del", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["delete", "files", "folders", "directories", "del", "remove", "destroy", "trash", "unlink", "clean", "cleaning", "cleanup", "rm", "rmrf", "<PERSON><PERSON><PERSON>", "rmdir", "glob", "gulpfriendly", "file", "folder", "directory", "dir", "fs", "filesystem"], "dependencies": {"@types/glob": "^7.1.1", "globby": "^6.1.0", "is-path-cwd": "^2.0.0", "is-path-in-cwd": "^2.0.0", "p-map": "^2.0.0", "pify": "^4.0.1", "rimraf": "^2.6.3"}, "devDependencies": {"ava": "^1.4.1", "make-dir": "^2.1.0", "tempy": "^0.2.1", "tsd": "^0.7.1", "xo": "^0.24.0"}}