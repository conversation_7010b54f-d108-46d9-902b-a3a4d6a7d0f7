<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolBox Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div id="app" class="app-container">
        <!-- Header with Voice Input -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="../icons/tb32.png" alt="ToolBox" class="logo">
                    <span class="app-title">ToolBox Pro</span>
                </div>
                <div class="voice-section">
                    <button id="voiceBtn" class="voice-btn" title="Voice Input (Ctrl+Shift+V)">
                        <svg class="mic-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                            <line x1="12" y1="19" x2="12" y2="23"></line>
                            <line x1="8" y1="23" x2="16" y2="23"></line>
                        </svg>
                    </button>
                    <div id="voiceIndicator" class="voice-indicator hidden">
                        <div class="voice-wave"></div>
                        <span class="voice-text">Listening...</span>
                    </div>
                </div>
            </div>
            <div id="searchInput" class="search-input-container hidden">
                <input type="text" id="searchField" placeholder="Ask ISAA or search page..." class="search-field">
                <button id="searchSubmit" class="search-submit">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </button>
            </div>
        </header>

        <!-- Tab Navigation -->
        <nav class="tab-navigation">
            <button class="tab-btn active" data-tab="isaa">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                <span>ISAA Chat</span>
            </button>
            <button class="tab-btn" data-tab="search">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
                <span>Live Search</span>
            </button>
            <button class="tab-btn" data-tab="passwords">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <circle cx="12" cy="16" r="1"></circle>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg>
                <span>Passwords</span>
            </button>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- ISAA Chat Panel -->
            <div id="isaaPanelContent" class="panel-content active">
                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="welcome-message">
                            <div class="isaa-avatar">🤖</div>
                            <div class="message-content">
                                <p>Hello! I'm ISAA, your AI assistant. I can help you:</p>
                                <ul>
                                    <li>Navigate and interact with web pages</li>
                                    <li>Download files and export data</li>
                                    <li>Answer questions about page content</li>
                                    <li>Automate web tasks</li>
                                </ul>
                                <p>How can I assist you today?</p>
                            </div>
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <input type="text" id="chatInput" placeholder="Ask ISAA anything..." class="chat-input">
                        <button id="chatSend" class="chat-send-btn">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9"></polygon>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Live Search Panel -->
            <div id="searchPanelContent" class="panel-content">
                <div class="search-container">
                    <div class="search-header">
                        <input type="text" id="liveSearchInput" placeholder="Search current page..." class="live-search-input">
                        <div class="search-options">
                            <button id="indexPageBtn" class="option-btn">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                </svg>
                                Index Page
                            </button>
                        </div>
                    </div>
                    <div id="searchResults" class="search-results">
                        <div class="no-results">
                            <p>Start typing to search the current page...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Password Manager Panel -->
            <div id="passwordsPanelContent" class="panel-content">
                <div class="password-container">
                    <div class="password-header">
                        <div class="password-actions">
                            <button id="autofillBtn" class="action-btn primary">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"></path>
                                    <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                                    <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                                </svg>
                                Auto-fill
                            </button>
                            <button id="generateBtn" class="action-btn">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                </svg>
                                Generate
                            </button>
                            <button id="importBtn" class="action-btn">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                </svg>
                                Import
                            </button>
                        </div>
                    </div>
                    <div id="passwordList" class="password-list">
                        <div class="loading-passwords">
                            <div class="spinner"></div>
                            <p>Loading passwords...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="connection-status">
                <div id="connectionIndicator" class="status-indicator connecting">
                    <div class="status-dot"></div>
                    <span id="connectionText">Connecting...</span>
                </div>
            </div>
            <div class="page-info">
                <span id="pageTitle">Loading...</span>
            </div>
        </footer>
    </div>

    <!-- Modals and Overlays -->
    <div id="voiceOverlay" class="voice-overlay hidden">
        <div class="voice-modal">
            <div class="voice-animation">
                <div class="voice-circle"></div>
                <div class="voice-pulse"></div>
            </div>
            <p class="voice-status">Listening...</p>
            <p class="voice-transcript"></p>
            <button class="voice-close">×</button>
        </div>
    </div>

    <div id="totpModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>2FA Code</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <div class="totp-display">
                    <div class="totp-code" id="totpCode">000000</div>
                    <div class="totp-timer">
                        <div class="timer-circle">
                            <div class="timer-fill" id="totpTimer"></div>
                        </div>
                        <span id="totpTimeLeft">30</span>
                    </div>
                </div>
                <p class="totp-account" id="totpAccount">Account</p>
                <button id="copyTotpBtn" class="copy-btn">Copy Code</button>
            </div>
        </div>
    </div>

    <script src="popup.js" type="module"></script>
</body>
</html>
