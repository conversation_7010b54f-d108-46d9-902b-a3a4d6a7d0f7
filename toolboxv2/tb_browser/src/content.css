/* ToolBox Pro - Content Script Styles */
/* Styles for page interaction elements */

/* Form indicators */
.tb-form-indicator {
    position: absolute !important;
    top: -10px !important;
    right: -10px !important;
    width: 20px !important;
    height: 20px !important;
    background: #2E86AB !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 10px !important;
    z-index: 10000 !important;
    opacity: 0.7 !important;
    transition: opacity 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(46, 134, 171, 0.3) !important;
}

.tb-form-indicator:hover {
    opacity: 1 !important;
    transform: scale(1.1) !important;
}

/* Search highlights */
.tb-search-highlight {
    outline: 2px solid #2E86AB !important;
    outline-offset: 2px !important;
    background: rgba(46, 134, 171, 0.1) !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
}

.tb-highlight-overlay {
    position: fixed !important;
    background: rgba(46, 134, 171, 0.2) !important;
    border: 2px solid #2E86AB !important;
    border-radius: 4px !important;
    pointer-events: none !important;
    z-index: 10000 !important;
    animation: tbHighlightPulse 2s ease-in-out !important;
}

/* Autofill success indicator */
.tb-autofill-success {
    position: absolute !important;
    top: -30px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: #4CAF50 !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    z-index: 10000 !important;
    animation: tbAutofillSuccess 3s ease-in-out forwards !important;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3) !important;
    white-space: nowrap !important;
}

/* Gesture feedback */
.tb-gesture-feedback {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 16px 24px !important;
    border-radius: 12px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    z-index: 10001 !important;
    animation: tbGestureFeedback 1.5s ease-in-out forwards !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Voice overlay for content script */
.tb-voice-overlay {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 12px 16px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    z-index: 10001 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    animation: tbSlideIn 0.3s ease-out !important;
}

.tb-voice-wave {
    width: 12px !important;
    height: 12px !important;
    background: #2E86AB !important;
    border-radius: 50% !important;
    animation: tbVoiceWave 1.5s ease-in-out infinite !important;
}

/* Page indexing indicator */
.tb-indexing-indicator {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    background: rgba(46, 134, 171, 0.9) !important;
    color: white !important;
    padding: 12px 16px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    z-index: 10001 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    animation: tbSlideUp 0.3s ease-out !important;
}

.tb-indexing-spinner {
    width: 16px !important;
    height: 16px !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-top: 2px solid white !important;
    border-radius: 50% !important;
    animation: tbSpin 1s linear infinite !important;
}

/* Scroll indicators */
.tb-scroll-indicator {
    position: fixed !important;
    right: 20px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: rgba(46, 134, 171, 0.9) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 16px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    z-index: 10001 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    animation: tbFadeInOut 2s ease-in-out forwards !important;
}

/* Navigation feedback */
.tb-nav-feedback {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 20px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    z-index: 10001 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    animation: tbSlideDown 0.3s ease-out, tbFadeOut 2s ease-in-out 1s forwards !important;
}

/* Animations */
@keyframes tbHighlightPulse {
    0%, 100% { 
        opacity: 0.3; 
        transform: scale(1); 
    }
    50% { 
        opacity: 0.6; 
        transform: scale(1.02); 
    }
}

@keyframes tbAutofillSuccess {
    0% { 
        opacity: 0; 
        transform: translateX(-50%) translateY(-10px); 
    }
    20% { 
        opacity: 1; 
        transform: translateX(-50%) translateY(0); 
    }
    80% { 
        opacity: 1; 
        transform: translateX(-50%) translateY(0); 
    }
    100% { 
        opacity: 0; 
        transform: translateX(-50%) translateY(-10px); 
    }
}

@keyframes tbGestureFeedback {
    0% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.8); 
    }
    20% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1); 
    }
    80% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1); 
    }
    100% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.8); 
    }
}

@keyframes tbSlideIn {
    0% { 
        opacity: 0; 
        transform: translateX(100%); 
    }
    100% { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

@keyframes tbSlideUp {
    0% { 
        opacity: 0; 
        transform: translateY(100%); 
    }
    100% { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes tbSlideDown {
    0% { 
        opacity: 0; 
        transform: translateX(-50%) translateY(-100%); 
    }
    100% { 
        opacity: 1; 
        transform: translateX(-50%) translateY(0); 
    }
}

@keyframes tbFadeInOut {
    0% { opacity: 0; }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes tbFadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes tbVoiceWave {
    0%, 100% { 
        transform: scale(1); 
        opacity: 1; 
    }
    50% { 
        transform: scale(1.2); 
        opacity: 0.7; 
    }
}

@keyframes tbSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tb-form-indicator {
        width: 16px !important;
        height: 16px !important;
        font-size: 8px !important;
    }
    
    .tb-autofill-success {
        font-size: 11px !important;
        padding: 6px 12px !important;
    }
    
    .tb-voice-overlay,
    .tb-indexing-indicator,
    .tb-nav-feedback {
        font-size: 11px !important;
        padding: 8px 12px !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .tb-search-highlight {
        outline-width: 3px !important;
        background: rgba(46, 134, 171, 0.3) !important;
    }
    
    .tb-form-indicator {
        border: 2px solid white !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .tb-form-indicator,
    .tb-search-highlight,
    .tb-highlight-overlay,
    .tb-autofill-success,
    .tb-gesture-feedback,
    .tb-voice-overlay,
    .tb-indexing-indicator,
    .tb-scroll-indicator,
    .tb-nav-feedback {
        animation: none !important;
        transition: none !important;
    }
    
    .tb-voice-wave,
    .tb-indexing-spinner {
        animation: none !important;
    }
}
