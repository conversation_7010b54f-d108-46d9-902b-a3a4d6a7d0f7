<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolBox Pro Extension - Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            color: #2E86AB;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-form {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #2E86AB;
        }
        
        .btn {
            background: #2E86AB;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #1B4D72;
        }
        
        .content-sections {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .content-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        
        .instructions {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #2E86AB;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            color: #2E86AB;
            margin-bottom: 10px;
        }
        
        .test-list {
            list-style: none;
            padding: 0;
        }
        
        .test-list li {
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid #2E86AB;
        }
        
        .test-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 ToolBox Pro Extension - Test Page</h1>
        <p>This page is designed to test all features of the ToolBox Pro browser extension</p>
    </div>

    <div class="instructions">
        <h3>📋 Testing Instructions</h3>
        <p><strong>Before testing:</strong> Make sure the ToolBox Pro extension is loaded and the ToolBox server is running on localhost:8080</p>
        <ul class="test-list">
            <li>Load the extension from the build folder in Chrome Developer Mode</li>
            <li>Ensure ToolBox server is running (check connection indicator in popup)</li>
            <li>Grant necessary permissions when prompted</li>
            <li>Test each feature systematically using the sections below</li>
        </ul>
    </div>

    <div class="feature-grid">
        <div class="feature-card">
            <h3>🎯 Gesture Detection</h3>
            <p><strong>Test Instructions:</strong></p>
            <ul>
                <li>Try swiping left/right with mouse or touch</li>
                <li>Swipe up/down for dynamic scrolling</li>
                <li>Double-click anywhere to open popup</li>
                <li>Use Alt+Arrow keys for keyboard gestures</li>
            </ul>
            <div class="highlight">
                <strong>Expected:</strong> Navigation and scrolling should work smoothly with visual feedback
            </div>
        </div>

        <div class="feature-card">
            <h3>🎤 Voice Input</h3>
            <p><strong>Test Instructions:</strong></p>
            <ul>
                <li>Click the microphone icon in popup</li>
                <li>Use Ctrl+Shift+V keyboard shortcut</li>
                <li>Try voice commands like "search test" or "password fill"</li>
                <li>Test wake words: "toolbox", "isaa"</li>
            </ul>
            <div class="highlight">
                <strong>Expected:</strong> Voice recognition with visual feedback and command execution
            </div>
        </div>

        <div class="feature-card">
            <h3>🤖 ISAA AI Assistant</h3>
            <p><strong>Test Instructions:</strong></p>
            <ul>
                <li>Open popup and go to ISAA Chat tab</li>
                <li>Ask questions about this page</li>
                <li>Try commands like "summarize this page"</li>
                <li>Test context menu "Ask ISAA about this"</li>
            </ul>
            <div class="highlight">
                <strong>Expected:</strong> AI responses with page context awareness
            </div>
        </div>

        <div class="feature-card">
            <h3>🔍 Live Search</h3>
            <p><strong>Test Instructions:</strong></p>
            <ul>
                <li>Go to Live Search tab in popup</li>
                <li>Search for "test", "form", "feature"</li>
                <li>Click "Index Page" button first</li>
                <li>Use "Scroll to" and "Ask ISAA" buttons</li>
            </ul>
            <div class="highlight">
                <strong>Expected:</strong> Real-time search with highlighting and navigation
            </div>
        </div>

        <div class="feature-card">
            <h3>🔐 Password Manager</h3>
            <p><strong>Test Instructions:</strong></p>
            <ul>
                <li>Go to Passwords tab in popup</li>
                <li>Try auto-fill on the form below</li>
                <li>Generate a new password</li>
                <li>Test 2FA code display (if available)</li>
            </ul>
            <div class="highlight">
                <strong>Expected:</strong> Password management with form detection and auto-fill
            </div>
        </div>

        <div class="feature-card">
            <h3>🎨 UI & Design</h3>
            <p><strong>Test Instructions:</strong></p>
            <ul>
                <li>Check glass morphism design</li>
                <li>Verify marine blue accents</li>
                <li>Test tab switching animations</li>
                <li>Check responsive behavior</li>
            </ul>
            <div class="highlight">
                <strong>Expected:</strong> Modern glass design with smooth animations
            </div>
        </div>
    </div>

    <div class="test-form">
        <h3>🧪 Test Login Form</h3>
        <p>Use this form to test password auto-fill and generation features:</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" placeholder="testuser">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" placeholder="Enter password">
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm password">
            </div>
            
            <button type="submit" class="btn">Test Submit</button>
        </form>
    </div>

    <div class="content-sections">
        <div class="content-section">
            <h2>📚 Sample Content for Search Testing</h2>
            <p>This section contains various types of content to test the live search functionality. The ToolBox Pro extension should be able to index and search through all of this content.</p>
            
            <h3>Features Overview</h3>
            <p>ToolBox Pro includes advanced gesture detection, ISAA AI assistant integration, voice input capabilities, live page search with auto-indexing, and a comprehensive password manager with 2FA support.</p>
            
            <h3>Technical Implementation</h3>
            <p>The extension uses Manifest V3 architecture with a service worker background script, content scripts for page interaction, and a modern popup interface built with vanilla JavaScript and CSS.</p>
            
            <h3>Security Considerations</h3>
            <p>All password data is encrypted using device keys, API communications are secured with HTTPS, and the extension follows strict content security policies to prevent XSS attacks.</p>
        </div>

        <div class="content-section">
            <h2>🔗 Navigation Links</h2>
            <p>Test the search functionality with these links:</p>
            <ul>
                <li><a href="#features">Features Section</a></li>
                <li><a href="#testing">Testing Instructions</a></li>
                <li><a href="#form">Test Form</a></li>
                <li><a href="https://github.com/MarkinHaus/ToolBoxV2" target="_blank">ToolBox Repository</a></li>
                <li><a href="https://toolbox.app" target="_blank">ToolBox Website</a></li>
            </ul>
        </div>

        <div class="content-section">
            <h2>📊 Test Data</h2>
            <p>This section contains structured data for testing search and indexing:</p>
            
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 12px; border: 1px solid #dee2e6;">Feature</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6;">Status</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6;">Priority</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Gesture Detection</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ Complete</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">High</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Voice Input</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ Complete</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">High</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">ISAA Integration</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ Complete</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Critical</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="content-section">
        <h2>✅ Testing Checklist</h2>
        <p>Use this checklist to systematically test all extension features:</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div>
                <h4>🎯 Gesture Tests</h4>
                <ul class="test-list">
                    <li>Left swipe navigation</li>
                    <li>Right swipe navigation</li>
                    <li>Up swipe scrolling</li>
                    <li>Double-click popup</li>
                    <li>Keyboard shortcuts</li>
                </ul>
            </div>
            
            <div>
                <h4>🎤 Voice Tests</h4>
                <ul class="test-list">
                    <li>Microphone activation</li>
                    <li>Voice recognition</li>
                    <li>Command processing</li>
                    <li>TTS feedback</li>
                    <li>Wake word detection</li>
                </ul>
            </div>
            
            <div>
                <h4>🔍 Search Tests</h4>
                <ul class="test-list">
                    <li>Page indexing</li>
                    <li>Live search</li>
                    <li>Result highlighting</li>
                    <li>Section navigation</li>
                    <li>ISAA integration</li>
                </ul>
            </div>
            
            <div>
                <h4>🔐 Password Tests</h4>
                <ul class="test-list">
                    <li>Form detection</li>
                    <li>Auto-fill functionality</li>
                    <li>Password generation</li>
                    <li>2FA code display</li>
                    <li>Import capabilities</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Test form submission
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Test form submitted! Check if ToolBox detected and can auto-fill this form.');
        });
        
        // Add some dynamic content for testing
        setTimeout(() => {
            const dynamicSection = document.createElement('div');
            dynamicSection.className = 'content-section';
            dynamicSection.innerHTML = `
                <h2>🔄 Dynamic Content</h2>
                <p>This content was added dynamically to test if the extension can index content that appears after page load.</p>
                <p>Search for "dynamic" to test this functionality.</p>
            `;
            document.body.appendChild(dynamicSection);
        }, 2000);
        
        console.log('🧪 ToolBox Pro test page loaded. Extension should detect this page and enable all features.');
    </script>
</body>
</html>
