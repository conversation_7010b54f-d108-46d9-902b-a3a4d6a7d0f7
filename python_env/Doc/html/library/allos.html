<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Generic Operating System Services" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/allos.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The modules described in this chapter provide interfaces to operating system features that are available on (almost) all operating systems, such as files and a clock. The interfaces are generally m..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The modules described in this chapter provide interfaces to operating system features that are available on (almost) all operating systems, such as files and a clock. The interfaces are generally m..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Generic Operating System Services &#8212; Python 3.12.9 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=23252803" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=9c91bd08"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.9 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="os — Miscellaneous operating system interfaces" href="os.html" />
    <link rel="prev" title="secrets — Generate secure random numbers for managing secrets" href="secrets.html" />
    
    <link rel="canonical" href="https://docs.python.org/3/library/allos.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="secrets.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="os.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> — Miscellaneous operating system interfaces</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/allos.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="os.html" title="os — Miscellaneous operating system interfaces"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="secrets.html" title="secrets — Generate secure random numbers for managing secrets"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.9 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Generic Operating System Services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="generic-operating-system-services">
<span id="allos"></span><h1>Generic Operating System Services<a class="headerlink" href="#generic-operating-system-services" title="Link to this heading">¶</a></h1>
<p>The modules described in this chapter provide interfaces to operating system
features that are available on (almost) all operating systems, such as files and
a clock.  The interfaces are generally modeled after the Unix or C interfaces,
but they are available on most other systems as well.  Here’s an overview:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="os.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> — Miscellaneous operating system interfaces</a><ul>
<li class="toctree-l2"><a class="reference internal" href="os.html#file-names-command-line-arguments-and-environment-variables">File Names, Command Line Arguments, and Environment Variables</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#python-utf-8-mode">Python UTF-8 Mode</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#process-parameters">Process Parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#file-object-creation">File Object Creation</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#file-descriptor-operations">File Descriptor Operations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="os.html#querying-the-size-of-a-terminal">Querying the size of a terminal</a></li>
<li class="toctree-l3"><a class="reference internal" href="os.html#inheritance-of-file-descriptors">Inheritance of File Descriptors</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="os.html#files-and-directories">Files and Directories</a><ul>
<li class="toctree-l3"><a class="reference internal" href="os.html#linux-extended-attributes">Linux extended attributes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="os.html#process-management">Process Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#interface-to-the-scheduler">Interface to the scheduler</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#miscellaneous-system-information">Miscellaneous System Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="os.html#random-numbers">Random numbers</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="io.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a><ul>
<li class="toctree-l2"><a class="reference internal" href="io.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="io.html#text-i-o">Text I/O</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#binary-i-o">Binary I/O</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#raw-i-o">Raw I/O</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="io.html#text-encoding">Text Encoding</a><ul>
<li class="toctree-l3"><a class="reference internal" href="io.html#opt-in-encodingwarning">Opt-in EncodingWarning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="io.html#high-level-module-interface">High-level Module Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="io.html#class-hierarchy">Class hierarchy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="io.html#i-o-base-classes">I/O Base Classes</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#raw-file-i-o">Raw File I/O</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#buffered-streams">Buffered Streams</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#id1">Text I/O</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="io.html#performance">Performance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="io.html#id2">Binary I/O</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#id3">Text I/O</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#multi-threading">Multi-threading</a></li>
<li class="toctree-l3"><a class="reference internal" href="io.html#reentrancy">Reentrancy</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="time.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="time.html#functions">Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="time.html#clock-id-constants">Clock ID Constants</a></li>
<li class="toctree-l2"><a class="reference internal" href="time.html#timezone-constants">Timezone Constants</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="argparse.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">argparse</span></code> — Parser for command-line options, arguments and subcommands</a><ul>
<li class="toctree-l2"><a class="reference internal" href="argparse.html#argumentparser-objects">ArgumentParser objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#prog">prog</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#usage">usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#description">description</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#epilog">epilog</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#parents">parents</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#formatter-class">formatter_class</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#prefix-chars">prefix_chars</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#fromfile-prefix-chars">fromfile_prefix_chars</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#argument-default">argument_default</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#allow-abbrev">allow_abbrev</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#conflict-handler">conflict_handler</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#add-help">add_help</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#exit-on-error">exit_on_error</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="argparse.html#the-add-argument-method">The add_argument() method</a><ul>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#name-or-flags">name or flags</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#action">action</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#nargs">nargs</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#const">const</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#default">default</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#type">type</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#choices">choices</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#required">required</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#help">help</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#metavar">metavar</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#dest">dest</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#action-classes">Action classes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="argparse.html#the-parse-args-method">The parse_args() method</a><ul>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#option-value-syntax">Option value syntax</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#invalid-arguments">Invalid arguments</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#arguments-containing">Arguments containing <code class="docutils literal notranslate"><span class="pre">-</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#argument-abbreviations-prefix-matching">Argument abbreviations (prefix matching)</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#beyond-sys-argv">Beyond <code class="docutils literal notranslate"><span class="pre">sys.argv</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#the-namespace-object">The Namespace object</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="argparse.html#other-utilities">Other utilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#sub-commands">Sub-commands</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#filetype-objects">FileType objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#argument-groups">Argument groups</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#mutual-exclusion">Mutual exclusion</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#parser-defaults">Parser defaults</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#printing-help">Printing help</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#partial-parsing">Partial parsing</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#customizing-file-parsing">Customizing file parsing</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#exiting-methods">Exiting methods</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#intermixed-parsing">Intermixed parsing</a></li>
<li class="toctree-l3"><a class="reference internal" href="argparse.html#registering-custom-types-or-actions">Registering custom types or actions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="argparse.html#exceptions">Exceptions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../howto/argparse.html">Argparse Tutorial</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#concepts">Concepts</a></li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#the-basics">The basics</a></li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#introducing-positional-arguments">Introducing Positional arguments</a></li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#introducing-optional-arguments">Introducing Optional arguments</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../howto/argparse.html#short-options">Short options</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#combining-positional-and-optional-arguments">Combining Positional and Optional arguments</a></li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#getting-a-little-more-advanced">Getting a little more advanced</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../howto/argparse.html#specifying-ambiguous-arguments">Specifying ambiguous arguments</a></li>
<li class="toctree-l5"><a class="reference internal" href="../howto/argparse.html#conflicting-options">Conflicting options</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#how-to-translate-the-argparse-output">How to translate the argparse output</a></li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#custom-type-converters">Custom type converters</a></li>
<li class="toctree-l4"><a class="reference internal" href="../howto/argparse.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../howto/argparse-optparse.html">Upgrading optparse code</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="getopt.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">getopt</span></code> — C-style parser for command line options</a></li>
<li class="toctree-l1"><a class="reference internal" href="logging.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging</span></code> — Logging facility for Python</a><ul>
<li class="toctree-l2"><a class="reference internal" href="logging.html#logger-objects">Logger Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#logging-levels">Logging Levels</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#handler-objects">Handler Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#formatter-objects">Formatter Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#filter-objects">Filter Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#logrecord-objects">LogRecord Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#logrecord-attributes">LogRecord attributes</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#loggeradapter-objects">LoggerAdapter Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#thread-safety">Thread Safety</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#module-level-functions">Module-Level Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#module-level-attributes">Module-Level Attributes</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.html#integration-with-the-warnings-module">Integration with the warnings module</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="logging.config.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.config</span></code> — Logging configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="logging.config.html#configuration-functions">Configuration functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.config.html#security-considerations">Security considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.config.html#configuration-dictionary-schema">Configuration dictionary schema</a><ul>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#dictionary-schema-details">Dictionary Schema Details</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#incremental-configuration">Incremental Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#object-connections">Object connections</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#user-defined-objects">User-defined objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#handler-configuration-order">Handler configuration order</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#access-to-external-objects">Access to external objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#access-to-internal-objects">Access to internal objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#import-resolution-and-custom-importers">Import resolution and custom importers</a></li>
<li class="toctree-l3"><a class="reference internal" href="logging.config.html#configuring-queuehandler-and-queuelistener">Configuring QueueHandler and QueueListener</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="logging.config.html#configuration-file-format">Configuration file format</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="logging.handlers.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code> — Logging handlers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#streamhandler">StreamHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#filehandler">FileHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#nullhandler">NullHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#watchedfilehandler">WatchedFileHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#baserotatinghandler">BaseRotatingHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#rotatingfilehandler">RotatingFileHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#timedrotatingfilehandler">TimedRotatingFileHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#sockethandler">SocketHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#datagramhandler">DatagramHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#sysloghandler">SysLogHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#nteventloghandler">NTEventLogHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#smtphandler">SMTPHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#memoryhandler">MemoryHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#httphandler">HTTPHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#queuehandler">QueueHandler</a></li>
<li class="toctree-l2"><a class="reference internal" href="logging.handlers.html#queuelistener">QueueListener</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="getpass.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">getpass</span></code> — Portable password input</a></li>
<li class="toctree-l1"><a class="reference internal" href="curses.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses</span></code> — Terminal handling for character-cell displays</a><ul>
<li class="toctree-l2"><a class="reference internal" href="curses.html#functions">Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="curses.html#window-objects">Window Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="curses.html#constants">Constants</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="curses.html#module-curses.textpad"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses.textpad</span></code> — Text input widget for curses programs</a><ul>
<li class="toctree-l2"><a class="reference internal" href="curses.html#textbox-objects">Textbox objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="curses.ascii.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses.ascii</span></code> — Utilities for ASCII characters</a></li>
<li class="toctree-l1"><a class="reference internal" href="curses.panel.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses.panel</span></code> — A panel stack extension for curses</a><ul>
<li class="toctree-l2"><a class="reference internal" href="curses.panel.html#functions">Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="curses.panel.html#panel-objects">Panel Objects</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="platform.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code> —  Access to underlying platform’s identifying data</a><ul>
<li class="toctree-l2"><a class="reference internal" href="platform.html#cross-platform">Cross Platform</a></li>
<li class="toctree-l2"><a class="reference internal" href="platform.html#java-platform">Java Platform</a></li>
<li class="toctree-l2"><a class="reference internal" href="platform.html#windows-platform">Windows Platform</a></li>
<li class="toctree-l2"><a class="reference internal" href="platform.html#macos-platform">macOS Platform</a></li>
<li class="toctree-l2"><a class="reference internal" href="platform.html#unix-platforms">Unix Platforms</a></li>
<li class="toctree-l2"><a class="reference internal" href="platform.html#linux-platforms">Linux Platforms</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="errno.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code> — Standard errno system symbols</a></li>
<li class="toctree-l1"><a class="reference internal" href="ctypes.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ctypes</span></code> — A foreign function library for Python</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ctypes.html#ctypes-tutorial">ctypes tutorial</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#loading-dynamic-link-libraries">Loading dynamic link libraries</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#accessing-functions-from-loaded-dlls">Accessing functions from loaded dlls</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#calling-functions">Calling functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#fundamental-data-types">Fundamental data types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#calling-functions-continued">Calling functions, continued</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#calling-variadic-functions">Calling variadic functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#calling-functions-with-your-own-custom-data-types">Calling functions with your own custom data types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#specifying-the-required-argument-types-function-prototypes">Specifying the required argument types (function prototypes)</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#return-types">Return types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#passing-pointers-or-passing-parameters-by-reference">Passing pointers (or: passing parameters by reference)</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#structures-and-unions">Structures and unions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#structure-union-alignment-and-byte-order">Structure/union alignment and byte order</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#bit-fields-in-structures-and-unions">Bit fields in structures and unions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#arrays">Arrays</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#pointers">Pointers</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#type-conversions">Type conversions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#incomplete-types">Incomplete Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#callback-functions">Callback functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#accessing-values-exported-from-dlls">Accessing values exported from dlls</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#surprises">Surprises</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#variable-sized-data-types">Variable-sized data types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ctypes.html#ctypes-reference">ctypes reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#finding-shared-libraries">Finding shared libraries</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#loading-shared-libraries">Loading shared libraries</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#foreign-functions">Foreign functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#function-prototypes">Function prototypes</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#utility-functions">Utility functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#data-types">Data types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#ctypes-fundamental-data-types-2">Fundamental data types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#structured-data-types">Structured data types</a></li>
<li class="toctree-l3"><a class="reference internal" href="ctypes.html#arrays-and-pointers">Arrays and pointers</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="secrets.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="os.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> — Miscellaneous operating system interfaces</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/allos.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="os.html" title="os — Miscellaneous operating system interfaces"
             >next</a> |</li>
        <li class="right" >
          <a href="secrets.html" title="secrets — Generate secure random numbers for managing secrets"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.9 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Generic Operating System Services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Feb 04, 2025 (15:07 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.1.3.
    </div>

  </body>
</html>