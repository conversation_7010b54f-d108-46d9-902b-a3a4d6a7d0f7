<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tty — Terminal control functions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tty.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tty.py The tty module defines functions for putting the tty into cbreak and raw modes. Availability: Unix. Because it requires the termios module, it will work only on Unix. The tt..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tty.py The tty module defines functions for putting the tty into cbreak and raw modes. Availability: Unix. Because it requires the termios module, it will work only on Unix. The tt..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tty — Terminal control functions &#8212; Python 3.12.9 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=23252803" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=9c91bd08"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.9 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="pty — Pseudo-terminal utilities" href="pty.html" />
    <link rel="prev" title="termios — POSIX style tty control" href="termios.html" />
    
    <link rel="canonical" href="https://docs.python.org/3/library/tty.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="termios.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code> — POSIX style tty control</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pty.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code> — Pseudo-terminal utilities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tty.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pty.html" title="pty — Pseudo-terminal utilities"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="termios.html" title="termios — POSIX style tty control"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.9 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" accesskey="U">Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tty">
<span id="tty-terminal-control-functions"></span><h1><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions<a class="headerlink" href="#module-tty" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="extlink-source reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tty.py">Lib/tty.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-tty" title="tty: Utility functions that perform common terminal control operations. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code></a> module defines functions for putting the tty into cbreak and raw
modes.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<p>Because it requires the <a class="reference internal" href="termios.html#module-termios" title="termios: POSIX style tty control. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code></a> module, it will work only on Unix.</p>
<p>The <a class="reference internal" href="#module-tty" title="tty: Utility functions that perform common terminal control operations. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="tty.cfmakeraw">
<span class="sig-prename descclassname"><span class="pre">tty.</span></span><span class="sig-name descname"><span class="pre">cfmakeraw</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tty.cfmakeraw" title="Link to this definition">¶</a></dt>
<dd><p>Convert the tty attribute list <em>mode</em>, which is a list like the one returned
by <a class="reference internal" href="termios.html#termios.tcgetattr" title="termios.tcgetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">termios.tcgetattr()</span></code></a>, to that of a tty in raw mode.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tty.cfmakecbreak">
<span class="sig-prename descclassname"><span class="pre">tty.</span></span><span class="sig-name descname"><span class="pre">cfmakecbreak</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tty.cfmakecbreak" title="Link to this definition">¶</a></dt>
<dd><p>Convert the tty attribute list <em>mode</em>, which is a list like the one returned
by <a class="reference internal" href="termios.html#termios.tcgetattr" title="termios.tcgetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">termios.tcgetattr()</span></code></a>, to that of a tty in cbreak mode.</p>
<p>This clears the <code class="docutils literal notranslate"><span class="pre">ECHO</span></code> and <code class="docutils literal notranslate"><span class="pre">ICANON</span></code> local mode flags in <em>mode</em> as well
as setting the minimum input to 1 byte with no delay.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12.2: </span>The <code class="docutils literal notranslate"><span class="pre">ICRNL</span></code> flag is no longer cleared. This matches Linux and macOS
<code class="docutils literal notranslate"><span class="pre">stty</span> <span class="pre">cbreak</span></code> behavior and what <a class="reference internal" href="#tty.setcbreak" title="tty.setcbreak"><code class="xref py py-func docutils literal notranslate"><span class="pre">setcbreak()</span></code></a> historically did.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tty.setraw">
<span class="sig-prename descclassname"><span class="pre">tty.</span></span><span class="sig-name descname"><span class="pre">setraw</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">when</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">termios.TCSAFLUSH</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tty.setraw" title="Link to this definition">¶</a></dt>
<dd><p>Change the mode of the file descriptor <em>fd</em> to raw. If <em>when</em> is omitted, it
defaults to <a class="reference internal" href="termios.html#termios.TCSAFLUSH" title="termios.TCSAFLUSH"><code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TCSAFLUSH</span></code></a>, and is passed to
<a class="reference internal" href="termios.html#termios.tcsetattr" title="termios.tcsetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">termios.tcsetattr()</span></code></a>. The return value of <a class="reference internal" href="termios.html#termios.tcgetattr" title="termios.tcgetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">termios.tcgetattr()</span></code></a>
is saved before setting <em>fd</em> to raw mode; this value is returned.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The return value is now the original tty attributes, instead of <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tty.setcbreak">
<span class="sig-prename descclassname"><span class="pre">tty.</span></span><span class="sig-name descname"><span class="pre">setcbreak</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">when</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">termios.TCSAFLUSH</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tty.setcbreak" title="Link to this definition">¶</a></dt>
<dd><p>Change the mode of file descriptor <em>fd</em> to cbreak. If <em>when</em> is omitted, it
defaults to <a class="reference internal" href="termios.html#termios.TCSAFLUSH" title="termios.TCSAFLUSH"><code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TCSAFLUSH</span></code></a>, and is passed to
<a class="reference internal" href="termios.html#termios.tcsetattr" title="termios.tcsetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">termios.tcsetattr()</span></code></a>. The return value of <a class="reference internal" href="termios.html#termios.tcgetattr" title="termios.tcgetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">termios.tcgetattr()</span></code></a>
is saved before setting <em>fd</em> to cbreak mode; this value is returned.</p>
<p>This clears the <code class="docutils literal notranslate"><span class="pre">ECHO</span></code> and <code class="docutils literal notranslate"><span class="pre">ICANON</span></code> local mode flags as well as setting
the minimum input to 1 byte with no delay.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The return value is now the original tty attributes, instead of <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12.2: </span>The <code class="docutils literal notranslate"><span class="pre">ICRNL</span></code> flag is no longer cleared. This restores the behavior
of Python 3.11 and earlier as well as matching what Linux, macOS, &amp; BSDs
describe in their <code class="docutils literal notranslate"><span class="pre">stty(1)</span></code> man pages regarding cbreak mode.</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="termios.html#module-termios" title="termios: POSIX style tty control. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code></a></dt><dd><p>Low-level terminal control interface.</p>
</dd>
</dl>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="termios.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code> — POSIX style tty control</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pty.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pty</span></code> — Pseudo-terminal utilities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tty.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pty.html" title="pty — Pseudo-terminal utilities"
             >next</a> |</li>
        <li class="right" >
          <a href="termios.html" title="termios — POSIX style tty control"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.9 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" >Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Feb 04, 2025 (15:07 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.1.3.
    </div>

  </body>
</html>