# This file is auto-generated by the CDP protocol generator.
# Do not edit this file manually as your changes will be overwritten.
# Generated from Chrome DevTools Protocol specifications.

"""CDP DeviceAccess Domain Library"""

from typing import Any, Dict, Optional, cast

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ...client import CDPClient
    from .commands import CancelPromptParameters
    from .commands import SelectPromptParameters

class DeviceAccessClient:
    """Client for DeviceAccess domain commands."""

    def __init__(self, client: 'CDPClient'):
        self._client = client

    async def enable(
        self,
        params: None = None,
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Enable events in this domain."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="DeviceAccess.enable",
            params=params,
            session_id=session_id,
        ))

    async def disable(
        self,
        params: None = None,
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Disable events in this domain."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="DeviceAccess.disable",
            params=params,
            session_id=session_id,
        ))

    async def selectPrompt(
        self,
        params: "SelectPromptParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Select a device in response to a DeviceAccess.deviceRequestPrompted event."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="DeviceAccess.selectPrompt",
            params=params,
            session_id=session_id,
        ))

    async def cancelPrompt(
        self,
        params: "CancelPromptParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Cancel a prompt in response to a DeviceAccess.deviceRequestPrompted event."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="DeviceAccess.cancelPrompt",
            params=params,
            session_id=session_id,
        ))


