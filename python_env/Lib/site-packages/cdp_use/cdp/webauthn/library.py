# This file is auto-generated by the CDP protocol generator.
# Do not edit this file manually as your changes will be overwritten.
# Generated from Chrome DevTools Protocol specifications.

"""CDP WebAuthn Domain Library"""

from typing import Any, Dict, Optional, cast

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ...client import CDPClient
    from .commands import AddCredentialParameters
    from .commands import AddVirtualAuthenticatorParameters
    from .commands import AddVirtualAuthenticatorReturns
    from .commands import ClearCredentialsParameters
    from .commands import EnableParameters
    from .commands import GetCredentialParameters
    from .commands import GetCredentialReturns
    from .commands import GetCredentialsParameters
    from .commands import GetCredentialsReturns
    from .commands import RemoveCredentialParameters
    from .commands import RemoveVirtualAuthenticatorParameters
    from .commands import SetAutomaticPresenceSimulationParameters
    from .commands import SetCredentialPropertiesParameters
    from .commands import SetResponseOverrideBitsParameters
    from .commands import SetUserVerifiedParameters

class WebAuthnClient:
    """Client for WebAuthn domain commands."""

    def __init__(self, client: 'CDPClient'):
        self._client = client

    async def enable(
        self,
        params: Optional["EnableParameters"] = None,
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Enable the WebAuthn domain and start intercepting credential storage and
retrieval with a virtual authenticator."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.enable",
            params=params,
            session_id=session_id,
        ))

    async def disable(
        self,
        params: None = None,
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Disable the WebAuthn domain."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.disable",
            params=params,
            session_id=session_id,
        ))

    async def addVirtualAuthenticator(
        self,
        params: "AddVirtualAuthenticatorParameters",
        session_id: Optional[str] = None,
    ) -> "AddVirtualAuthenticatorReturns":
        """Creates and adds a virtual authenticator."""
        return cast("AddVirtualAuthenticatorReturns", await self._client.send_raw(
            method="WebAuthn.addVirtualAuthenticator",
            params=params,
            session_id=session_id,
        ))

    async def setResponseOverrideBits(
        self,
        params: "SetResponseOverrideBitsParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Resets parameters isBogusSignature, isBadUV, isBadUP to false if they are not present."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.setResponseOverrideBits",
            params=params,
            session_id=session_id,
        ))

    async def removeVirtualAuthenticator(
        self,
        params: "RemoveVirtualAuthenticatorParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Removes the given authenticator."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.removeVirtualAuthenticator",
            params=params,
            session_id=session_id,
        ))

    async def addCredential(
        self,
        params: "AddCredentialParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Adds the credential to the specified authenticator."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.addCredential",
            params=params,
            session_id=session_id,
        ))

    async def getCredential(
        self,
        params: "GetCredentialParameters",
        session_id: Optional[str] = None,
    ) -> "GetCredentialReturns":
        """Returns a single credential stored in the given virtual authenticator that
matches the credential ID."""
        return cast("GetCredentialReturns", await self._client.send_raw(
            method="WebAuthn.getCredential",
            params=params,
            session_id=session_id,
        ))

    async def getCredentials(
        self,
        params: "GetCredentialsParameters",
        session_id: Optional[str] = None,
    ) -> "GetCredentialsReturns":
        """Returns all the credentials stored in the given virtual authenticator."""
        return cast("GetCredentialsReturns", await self._client.send_raw(
            method="WebAuthn.getCredentials",
            params=params,
            session_id=session_id,
        ))

    async def removeCredential(
        self,
        params: "RemoveCredentialParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Removes a credential from the authenticator."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.removeCredential",
            params=params,
            session_id=session_id,
        ))

    async def clearCredentials(
        self,
        params: "ClearCredentialsParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Clears all the credentials from the specified device."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.clearCredentials",
            params=params,
            session_id=session_id,
        ))

    async def setUserVerified(
        self,
        params: "SetUserVerifiedParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Sets whether User Verification succeeds or fails for an authenticator.
The default is true."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.setUserVerified",
            params=params,
            session_id=session_id,
        ))

    async def setAutomaticPresenceSimulation(
        self,
        params: "SetAutomaticPresenceSimulationParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Sets whether tests of user presence will succeed immediately (if true) or fail to resolve (if false) for an authenticator.
The default is true."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.setAutomaticPresenceSimulation",
            params=params,
            session_id=session_id,
        ))

    async def setCredentialProperties(
        self,
        params: "SetCredentialPropertiesParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Allows setting credential properties.
https://w3c.github.io/webauthn/#sctn-automation-set-credential-properties"""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="WebAuthn.setCredentialProperties",
            params=params,
            session_id=session_id,
        ))


