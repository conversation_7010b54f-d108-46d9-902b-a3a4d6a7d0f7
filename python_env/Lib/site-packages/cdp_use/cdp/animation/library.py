# This file is auto-generated by the CDP protocol generator.
# Do not edit this file manually as your changes will be overwritten.
# Generated from Chrome DevTools Protocol specifications.

"""CDP Animation Domain Library"""

from typing import Any, Dict, Optional, cast

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ...client import CDPClient
    from .commands import GetCurrentTimeParameters
    from .commands import GetCurrentTimeReturns
    from .commands import GetPlaybackRateReturns
    from .commands import ReleaseAnimationsParameters
    from .commands import ResolveAnimationParameters
    from .commands import ResolveAnimationReturns
    from .commands import SeekAnimationsParameters
    from .commands import SetPausedParameters
    from .commands import SetPlaybackRateParameters
    from .commands import SetTimingParameters

class AnimationClient:
    """Client for Animation domain commands."""

    def __init__(self, client: 'CDPClient'):
        self._client = client

    async def disable(
        self,
        params: None = None,
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Disables animation domain notifications."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.disable",
            params=params,
            session_id=session_id,
        ))

    async def enable(
        self,
        params: None = None,
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Enables animation domain notifications."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.enable",
            params=params,
            session_id=session_id,
        ))

    async def getCurrentTime(
        self,
        params: "GetCurrentTimeParameters",
        session_id: Optional[str] = None,
    ) -> "GetCurrentTimeReturns":
        """Returns the current time of the an animation."""
        return cast("GetCurrentTimeReturns", await self._client.send_raw(
            method="Animation.getCurrentTime",
            params=params,
            session_id=session_id,
        ))

    async def getPlaybackRate(
        self,
        params: None = None,
        session_id: Optional[str] = None,
    ) -> "GetPlaybackRateReturns":
        """Gets the playback rate of the document timeline."""
        return cast("GetPlaybackRateReturns", await self._client.send_raw(
            method="Animation.getPlaybackRate",
            params=params,
            session_id=session_id,
        ))

    async def releaseAnimations(
        self,
        params: "ReleaseAnimationsParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Releases a set of animations to no longer be manipulated."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.releaseAnimations",
            params=params,
            session_id=session_id,
        ))

    async def resolveAnimation(
        self,
        params: "ResolveAnimationParameters",
        session_id: Optional[str] = None,
    ) -> "ResolveAnimationReturns":
        """Gets the remote object of the Animation."""
        return cast("ResolveAnimationReturns", await self._client.send_raw(
            method="Animation.resolveAnimation",
            params=params,
            session_id=session_id,
        ))

    async def seekAnimations(
        self,
        params: "SeekAnimationsParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Seek a set of animations to a particular time within each animation."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.seekAnimations",
            params=params,
            session_id=session_id,
        ))

    async def setPaused(
        self,
        params: "SetPausedParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Sets the paused state of a set of animations."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.setPaused",
            params=params,
            session_id=session_id,
        ))

    async def setPlaybackRate(
        self,
        params: "SetPlaybackRateParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Sets the playback rate of the document timeline."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.setPlaybackRate",
            params=params,
            session_id=session_id,
        ))

    async def setTiming(
        self,
        params: "SetTimingParameters",
        session_id: Optional[str] = None,
    ) -> "Dict[str, Any]":
        """Sets the timing of an animation node."""
        return cast("Dict[str, Any]", await self._client.send_raw(
            method="Animation.setTiming",
            params=params,
            session_id=session_id,
        ))


