
ctypedef fused number_t:
    double complex
    double

cpdef number_t spherical_jn(Py_ssize_t n, number_t z, bint derivative=*) noexcept nogil
cpdef number_t spherical_yn(Py_ssize_t n, number_t z, bint derivative=*) noexcept nogil
cpdef number_t spherical_in(Py_ssize_t n, number_t z, bint derivative=*) noexcept nogil
cpdef number_t spherical_kn(Py_ssize_t n, number_t z, bint derivative=*) noexcept nogil

ctypedef fused Dd_number_t:
    double complex
    double

ctypedef fused df_number_t:
    double
    float

ctypedef fused dfg_number_t:
    double
    float
    long double

ctypedef fused dlp_number_t:
    double
    long
    Py_ssize_t

cpdef double voigt_profile(double x0, double x1, double x2) noexcept nogil
cpdef double agm(double x0, double x1) noexcept nogil
cdef void airy(Dd_number_t x0, Dd_number_t *y0, Dd_number_t *y1, Dd_number_t *y2, Dd_number_t *y3) noexcept nogil
cdef void airye(Dd_number_t x0, Dd_number_t *y0, Dd_number_t *y1, Dd_number_t *y2, Dd_number_t *y3) noexcept nogil
cpdef double bdtr(double x0, dlp_number_t x1, double x2) noexcept nogil
cpdef double bdtrc(double x0, dlp_number_t x1, double x2) noexcept nogil
cpdef double bdtri(double x0, dlp_number_t x1, double x2) noexcept nogil
cpdef double bdtrik(double x0, double x1, double x2) noexcept nogil
cpdef double bdtrin(double x0, double x1, double x2) noexcept nogil
cpdef double bei(double x0) noexcept nogil
cpdef double beip(double x0) noexcept nogil
cpdef double ber(double x0) noexcept nogil
cpdef double berp(double x0) noexcept nogil
cpdef double besselpoly(double x0, double x1, double x2) noexcept nogil
cpdef double beta(double x0, double x1) noexcept nogil
cpdef df_number_t betainc(df_number_t x0, df_number_t x1, df_number_t x2) noexcept nogil
cpdef df_number_t betaincc(df_number_t x0, df_number_t x1, df_number_t x2) noexcept nogil
cpdef df_number_t betaincinv(df_number_t x0, df_number_t x1, df_number_t x2) noexcept nogil
cpdef df_number_t betainccinv(df_number_t x0, df_number_t x1, df_number_t x2) noexcept nogil
cpdef double betaln(double x0, double x1) noexcept nogil
cpdef double binom(double x0, double x1) noexcept nogil
cpdef double boxcox(double x0, double x1) noexcept nogil
cpdef double boxcox1p(double x0, double x1) noexcept nogil
cpdef double btdtria(double x0, double x1, double x2) noexcept nogil
cpdef double btdtrib(double x0, double x1, double x2) noexcept nogil
cpdef double cbrt(double x0) noexcept nogil
cpdef double chdtr(double x0, double x1) noexcept nogil
cpdef double chdtrc(double x0, double x1) noexcept nogil
cpdef double chdtri(double x0, double x1) noexcept nogil
cpdef double chdtriv(double x0, double x1) noexcept nogil
cpdef double chndtr(double x0, double x1, double x2) noexcept nogil
cpdef double chndtridf(double x0, double x1, double x2) noexcept nogil
cpdef double chndtrinc(double x0, double x1, double x2) noexcept nogil
cpdef double chndtrix(double x0, double x1, double x2) noexcept nogil
cpdef double cosdg(double x0) noexcept nogil
cpdef double cosm1(double x0) noexcept nogil
cpdef double cotdg(double x0) noexcept nogil
cpdef Dd_number_t dawsn(Dd_number_t x0) noexcept nogil
cpdef double ellipe(double x0) noexcept nogil
cpdef double ellipeinc(double x0, double x1) noexcept nogil
cdef void ellipj(double x0, double x1, double *y0, double *y1, double *y2, double *y3) noexcept nogil
cpdef double ellipkinc(double x0, double x1) noexcept nogil
cpdef double ellipkm1(double x0) noexcept nogil
cpdef double ellipk(double x0) noexcept nogil
cpdef Dd_number_t elliprc(Dd_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t elliprd(Dd_number_t x0, Dd_number_t x1, Dd_number_t x2) noexcept nogil
cpdef Dd_number_t elliprf(Dd_number_t x0, Dd_number_t x1, Dd_number_t x2) noexcept nogil
cpdef Dd_number_t elliprg(Dd_number_t x0, Dd_number_t x1, Dd_number_t x2) noexcept nogil
cpdef Dd_number_t elliprj(Dd_number_t x0, Dd_number_t x1, Dd_number_t x2, Dd_number_t x3) noexcept nogil
cpdef double entr(double x0) noexcept nogil
cpdef Dd_number_t erf(Dd_number_t x0) noexcept nogil
cpdef Dd_number_t erfc(Dd_number_t x0) noexcept nogil
cpdef Dd_number_t erfcx(Dd_number_t x0) noexcept nogil
cpdef Dd_number_t erfi(Dd_number_t x0) noexcept nogil
cpdef df_number_t erfinv(df_number_t x0) noexcept nogil
cpdef double erfcinv(double x0) noexcept nogil
cpdef Dd_number_t eval_chebyc(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_chebys(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_chebyt(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_chebyu(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_gegenbauer(dlp_number_t x0, double x1, Dd_number_t x2) noexcept nogil
cpdef Dd_number_t eval_genlaguerre(dlp_number_t x0, double x1, Dd_number_t x2) noexcept nogil
cpdef double eval_hermite(Py_ssize_t x0, double x1) noexcept nogil
cpdef double eval_hermitenorm(Py_ssize_t x0, double x1) noexcept nogil
cpdef Dd_number_t eval_jacobi(dlp_number_t x0, double x1, double x2, Dd_number_t x3) noexcept nogil
cpdef Dd_number_t eval_laguerre(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_legendre(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_sh_chebyt(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_sh_chebyu(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t eval_sh_jacobi(dlp_number_t x0, double x1, double x2, Dd_number_t x3) noexcept nogil
cpdef Dd_number_t eval_sh_legendre(dlp_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t exp1(Dd_number_t x0) noexcept nogil
cpdef double exp10(double x0) noexcept nogil
cpdef double exp2(double x0) noexcept nogil
cpdef Dd_number_t expi(Dd_number_t x0) noexcept nogil
cpdef dfg_number_t expit(dfg_number_t x0) noexcept nogil
cpdef Dd_number_t expm1(Dd_number_t x0) noexcept nogil
cpdef double expn(dlp_number_t x0, double x1) noexcept nogil
cpdef double exprel(double x0) noexcept nogil
cpdef double fdtr(double x0, double x1, double x2) noexcept nogil
cpdef double fdtrc(double x0, double x1, double x2) noexcept nogil
cpdef double fdtri(double x0, double x1, double x2) noexcept nogil
cpdef double fdtridfd(double x0, double x1, double x2) noexcept nogil
cdef void fresnel(Dd_number_t x0, Dd_number_t *y0, Dd_number_t *y1) noexcept nogil
cpdef Dd_number_t gamma(Dd_number_t x0) noexcept nogil
cpdef double gammainc(double x0, double x1) noexcept nogil
cpdef double gammaincc(double x0, double x1) noexcept nogil
cpdef double gammainccinv(double x0, double x1) noexcept nogil
cpdef double gammaincinv(double x0, double x1) noexcept nogil
cpdef double gammaln(double x0) noexcept nogil
cpdef double gammasgn(double x0) noexcept nogil
cpdef double gdtr(double x0, double x1, double x2) noexcept nogil
cpdef double gdtrc(double x0, double x1, double x2) noexcept nogil
cpdef double gdtria(double x0, double x1, double x2) noexcept nogil
cpdef double gdtrib(double x0, double x1, double x2) noexcept nogil
cpdef double gdtrix(double x0, double x1, double x2) noexcept nogil
cpdef double complex hankel1(double x0, double complex x1) noexcept nogil
cpdef double complex hankel1e(double x0, double complex x1) noexcept nogil
cpdef double complex hankel2(double x0, double complex x1) noexcept nogil
cpdef double complex hankel2e(double x0, double complex x1) noexcept nogil
cpdef double huber(double x0, double x1) noexcept nogil
cpdef Dd_number_t hyp0f1(double x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t hyp1f1(double x0, double x1, Dd_number_t x2) noexcept nogil
cpdef Dd_number_t hyp2f1(double x0, double x1, double x2, Dd_number_t x3) noexcept nogil
cpdef double hyperu(double x0, double x1, double x2) noexcept nogil
cpdef double i0(double x0) noexcept nogil
cpdef double i0e(double x0) noexcept nogil
cpdef double i1(double x0) noexcept nogil
cpdef double i1e(double x0) noexcept nogil
cpdef double inv_boxcox(double x0, double x1) noexcept nogil
cpdef double inv_boxcox1p(double x0, double x1) noexcept nogil
cdef void it2i0k0(double x0, double *y0, double *y1) noexcept nogil
cdef void it2j0y0(double x0, double *y0, double *y1) noexcept nogil
cpdef double it2struve0(double x0) noexcept nogil
cdef void itairy(double x0, double *y0, double *y1, double *y2, double *y3) noexcept nogil
cdef void iti0k0(double x0, double *y0, double *y1) noexcept nogil
cdef void itj0y0(double x0, double *y0, double *y1) noexcept nogil
cpdef double itmodstruve0(double x0) noexcept nogil
cpdef double itstruve0(double x0) noexcept nogil
cpdef Dd_number_t iv(double x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t ive(double x0, Dd_number_t x1) noexcept nogil
cpdef double j0(double x0) noexcept nogil
cpdef double j1(double x0) noexcept nogil
cpdef Dd_number_t jv(double x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t jve(double x0, Dd_number_t x1) noexcept nogil
cpdef double k0(double x0) noexcept nogil
cpdef double k0e(double x0) noexcept nogil
cpdef double k1(double x0) noexcept nogil
cpdef double k1e(double x0) noexcept nogil
cpdef double kei(double x0) noexcept nogil
cpdef double keip(double x0) noexcept nogil
cdef void kelvin(double x0, double complex *y0, double complex *y1, double complex *y2, double complex *y3) noexcept nogil
cpdef double ker(double x0) noexcept nogil
cpdef double kerp(double x0) noexcept nogil
cpdef double kl_div(double x0, double x1) noexcept nogil
cpdef double kn(dlp_number_t x0, double x1) noexcept nogil
cpdef double kolmogi(double x0) noexcept nogil
cpdef double kolmogorov(double x0) noexcept nogil
cpdef Dd_number_t kv(double x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t kve(double x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t log1p(Dd_number_t x0) noexcept nogil
cpdef dfg_number_t log_expit(dfg_number_t x0) noexcept nogil
cpdef Dd_number_t log_ndtr(Dd_number_t x0) noexcept nogil
cpdef Dd_number_t loggamma(Dd_number_t x0) noexcept nogil
cpdef dfg_number_t logit(dfg_number_t x0) noexcept nogil
cpdef double lpmv(double x0, double x1, double x2) noexcept nogil
cpdef double mathieu_a(double x0, double x1) noexcept nogil
cpdef double mathieu_b(double x0, double x1) noexcept nogil
cdef void mathieu_cem(double x0, double x1, double x2, double *y0, double *y1) noexcept nogil
cdef void mathieu_modcem1(double x0, double x1, double x2, double *y0, double *y1) noexcept nogil
cdef void mathieu_modcem2(double x0, double x1, double x2, double *y0, double *y1) noexcept nogil
cdef void mathieu_modsem1(double x0, double x1, double x2, double *y0, double *y1) noexcept nogil
cdef void mathieu_modsem2(double x0, double x1, double x2, double *y0, double *y1) noexcept nogil
cdef void mathieu_sem(double x0, double x1, double x2, double *y0, double *y1) noexcept nogil
cdef void modfresnelm(double x0, double complex *y0, double complex *y1) noexcept nogil
cdef void modfresnelp(double x0, double complex *y0, double complex *y1) noexcept nogil
cpdef double modstruve(double x0, double x1) noexcept nogil
cpdef double nbdtr(dlp_number_t x0, dlp_number_t x1, double x2) noexcept nogil
cpdef double nbdtrc(dlp_number_t x0, dlp_number_t x1, double x2) noexcept nogil
cpdef double nbdtri(dlp_number_t x0, dlp_number_t x1, double x2) noexcept nogil
cpdef double nbdtrik(double x0, double x1, double x2) noexcept nogil
cpdef double nbdtrin(double x0, double x1, double x2) noexcept nogil
cpdef df_number_t ncfdtr(df_number_t x0, df_number_t x1, df_number_t x2, df_number_t x3) noexcept nogil
cpdef df_number_t ncfdtri(df_number_t x0, df_number_t x1, df_number_t x2, df_number_t x3) noexcept nogil
cpdef double ncfdtridfd(double x0, double x1, double x2, double x3) noexcept nogil
cpdef double ncfdtridfn(double x0, double x1, double x2, double x3) noexcept nogil
cpdef double ncfdtrinc(double x0, double x1, double x2, double x3) noexcept nogil
cpdef df_number_t nctdtr(df_number_t x0, df_number_t x1, df_number_t x2) noexcept nogil
cpdef double nctdtridf(double x0, double x1, double x2) noexcept nogil
cpdef double nctdtrinc(double x0, double x1, double x2) noexcept nogil
cpdef double nctdtrit(double x0, double x1, double x2) noexcept nogil
cpdef Dd_number_t ndtr(Dd_number_t x0) noexcept nogil
cpdef double ndtri(double x0) noexcept nogil
cpdef double nrdtrimn(double x0, double x1, double x2) noexcept nogil
cpdef double nrdtrisd(double x0, double x1, double x2) noexcept nogil
cdef void obl_ang1(double x0, double x1, double x2, double x3, double *y0, double *y1) noexcept nogil
cdef void obl_ang1_cv(double x0, double x1, double x2, double x3, double x4, double *y0, double *y1) noexcept nogil
cpdef double obl_cv(double x0, double x1, double x2) noexcept nogil
cdef void obl_rad1(double x0, double x1, double x2, double x3, double *y0, double *y1) noexcept nogil
cdef void obl_rad1_cv(double x0, double x1, double x2, double x3, double x4, double *y0, double *y1) noexcept nogil
cdef void obl_rad2(double x0, double x1, double x2, double x3, double *y0, double *y1) noexcept nogil
cdef void obl_rad2_cv(double x0, double x1, double x2, double x3, double x4, double *y0, double *y1) noexcept nogil
cpdef double owens_t(double x0, double x1) noexcept nogil
cdef void pbdv(double x0, double x1, double *y0, double *y1) noexcept nogil
cdef void pbvv(double x0, double x1, double *y0, double *y1) noexcept nogil
cdef void pbwa(double x0, double x1, double *y0, double *y1) noexcept nogil
cpdef double pdtr(double x0, double x1) noexcept nogil
cpdef double pdtrc(double x0, double x1) noexcept nogil
cpdef double pdtri(dlp_number_t x0, double x1) noexcept nogil
cpdef double pdtrik(double x0, double x1) noexcept nogil
cpdef double poch(double x0, double x1) noexcept nogil
cpdef df_number_t powm1(df_number_t x0, df_number_t x1) noexcept nogil
cdef void pro_ang1(double x0, double x1, double x2, double x3, double *y0, double *y1) noexcept nogil
cdef void pro_ang1_cv(double x0, double x1, double x2, double x3, double x4, double *y0, double *y1) noexcept nogil
cpdef double pro_cv(double x0, double x1, double x2) noexcept nogil
cdef void pro_rad1(double x0, double x1, double x2, double x3, double *y0, double *y1) noexcept nogil
cdef void pro_rad1_cv(double x0, double x1, double x2, double x3, double x4, double *y0, double *y1) noexcept nogil
cdef void pro_rad2(double x0, double x1, double x2, double x3, double *y0, double *y1) noexcept nogil
cdef void pro_rad2_cv(double x0, double x1, double x2, double x3, double x4, double *y0, double *y1) noexcept nogil
cpdef double pseudo_huber(double x0, double x1) noexcept nogil
cpdef Dd_number_t psi(Dd_number_t x0) noexcept nogil
cpdef double radian(double x0, double x1, double x2) noexcept nogil
cpdef double rel_entr(double x0, double x1) noexcept nogil
cpdef Dd_number_t rgamma(Dd_number_t x0) noexcept nogil
cpdef double round(double x0) noexcept nogil
cdef void shichi(Dd_number_t x0, Dd_number_t *y0, Dd_number_t *y1) noexcept nogil
cdef void sici(Dd_number_t x0, Dd_number_t *y0, Dd_number_t *y1) noexcept nogil
cpdef double sindg(double x0) noexcept nogil
cpdef double smirnov(dlp_number_t x0, double x1) noexcept nogil
cpdef double smirnovi(dlp_number_t x0, double x1) noexcept nogil
cpdef Dd_number_t spence(Dd_number_t x0) noexcept nogil
cpdef double complex sph_harm(dlp_number_t x0, dlp_number_t x1, double x2, double x3) noexcept nogil
cpdef double stdtr(double x0, double x1) noexcept nogil
cpdef double stdtridf(double x0, double x1) noexcept nogil
cpdef double stdtrit(double x0, double x1) noexcept nogil
cpdef double struve(double x0, double x1) noexcept nogil
cpdef double tandg(double x0) noexcept nogil
cpdef double tklmbda(double x0, double x1) noexcept nogil
cpdef double complex wofz(double complex x0) noexcept nogil
cpdef Dd_number_t wrightomega(Dd_number_t x0) noexcept nogil
cpdef Dd_number_t xlog1py(Dd_number_t x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t xlogy(Dd_number_t x0, Dd_number_t x1) noexcept nogil
cpdef double y0(double x0) noexcept nogil
cpdef double y1(double x0) noexcept nogil
cpdef double yn(dlp_number_t x0, double x1) noexcept nogil
cpdef Dd_number_t yv(double x0, Dd_number_t x1) noexcept nogil
cpdef Dd_number_t yve(double x0, Dd_number_t x1) noexcept nogil
cpdef double zetac(double x0) noexcept nogil
cpdef double wright_bessel(double x0, double x1, double x2) noexcept nogil
cpdef double log_wright_bessel(double x0, double x1, double x2) noexcept nogil
cpdef double ndtri_exp(double x0) noexcept nogil
