#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API nuclear_norm {
  using schema = at::Tensor (const at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nuclear_norm";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "nuclear_norm(Tensor self, bool keepdim=False) -> Tensor";
  static at::Tensor call(const at::Tensor & self, bool keepdim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, bool keepdim);
};

struct TORCH_API nuclear_norm_out {
  using schema = at::Tensor & (const at::Tensor &, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nuclear_norm";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "nuclear_norm.out(Tensor self, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, bool keepdim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, bool keepdim, at::Tensor & out);
};

struct TORCH_API nuclear_norm_dim {
  using schema = at::Tensor (const at::Tensor &, at::IntArrayRef, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nuclear_norm";
  static constexpr const char* overload_name = "dim";
  static constexpr const char* schema_str = "nuclear_norm.dim(Tensor self, int[2] dim, bool keepdim=False) -> Tensor";
  static at::Tensor call(const at::Tensor & self, at::IntArrayRef dim, bool keepdim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::IntArrayRef dim, bool keepdim);
};

struct TORCH_API nuclear_norm_dim_out {
  using schema = at::Tensor & (const at::Tensor &, at::IntArrayRef, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::nuclear_norm";
  static constexpr const char* overload_name = "dim_out";
  static constexpr const char* schema_str = "nuclear_norm.dim_out(Tensor self, int[2] dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
};

}} // namespace at::_ops
