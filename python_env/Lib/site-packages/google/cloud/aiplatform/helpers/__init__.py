# Copyright 2021 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.cloud.aiplatform.helpers import container_uri_builders

get_prebuilt_prediction_container_uri = (
    container_uri_builders.get_prebuilt_prediction_container_uri
)
is_prebuilt_prediction_container_uri = (
    container_uri_builders.is_prebuilt_prediction_container_uri
)
_get_closest_match_prebuilt_container_uri = (
    container_uri_builders._get_closest_match_prebuilt_container_uri
)

__all__ = (
    "get_prebuilt_prediction_container_uri",
    "is_prebuilt_prediction_container_uri",
    "_get_closest_match_prebuilt_container_uri",
)
